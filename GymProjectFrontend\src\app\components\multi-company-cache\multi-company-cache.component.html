<div class="multi-company-cache-component">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-1">
        <i class="fas fa-building me-2"></i>
        Multi-Company Cache Yönetimi
      </h2>
      <p class="text-muted mb-0">Tüm şirketlerin cache durumunu izleyin ve yönetin</p>
    </div>
    
    <div class="d-flex gap-2">
      <!-- Auto Refresh Toggle -->
      <button 
        class="btn btn-sm"
        [class.btn-success]="autoRefresh"
        [class.btn-outline-secondary]="!autoRefresh"
        (click)="toggleAutoRefresh()"
        title="Otomatik Yenileme">
        <i class="fas fa-sync-alt" [class.fa-spin]="autoRefresh"></i>
      </button>
      
      <!-- Manual Refresh -->
      <button 
        class="btn btn-primary btn-sm" 
        (click)="loadAllCompaniesStatistics()"
        [disabled]="isLoading">
        <i class="fas fa-refresh" [class.fa-spin]="isLoading"></i>
        Yenile
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading && !allCompaniesStats" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Yükleniyor...</span>
    </div>
    <p class="mt-2 text-muted">Cache istatistikleri yükleniyor...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading || allCompaniesStats">
    
    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs mb-4">
      <li class="nav-item">
        <button 
          class="nav-link"
          [class.active]="activeView === 'overview'"
          (click)="activeView = 'overview'">
          <i class="fas fa-chart-pie me-2"></i>Genel Bakış
        </button>
      </li>
      <li class="nav-item">
        <button 
          class="nav-link"
          [class.active]="activeView === 'companies'"
          (click)="activeView = 'companies'">
          <i class="fas fa-building me-2"></i>Şirketler
        </button>
      </li>
      <li class="nav-item">
        <button 
          class="nav-link"
          [class.active]="activeView === 'operations'"
          (click)="activeView = 'operations'">
          <i class="fas fa-cogs me-2"></i>Toplu İşlemler
        </button>
      </li>
    </ul>

    <!-- Overview Tab -->
    <div *ngIf="activeView === 'overview'" class="tab-content">
      <div class="row mb-4">
        <!-- Total Companies -->
        <div class="col-md-3 mb-3">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Toplam Şirket</h6>
                  <h3 class="mb-0">{{ totalCompanies }}</h3>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-building fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Total Cache Keys -->
        <div class="col-md-3 mb-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Toplam Cache Key</h6>
                  <h3 class="mb-0">{{ totalCacheKeys | number }}</h3>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-key fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Total Memory Usage -->
        <div class="col-md-3 mb-3">
          <div class="card bg-warning text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Toplam Memory</h6>
                  <h3 class="mb-0">{{ formatMemory(totalMemoryUsage) }}</h3>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-memory fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Average Keys Per Company -->
        <div class="col-md-3 mb-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h6 class="card-title">Ortalama Key/Şirket</h6>
                  <h3 class="mb-0">{{ averageKeysPerCompany | number:'1.0-0' }}</h3>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-chart-bar fa-2x opacity-75"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Companies by Cache Usage -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-trophy me-2"></i>
            En Yüksek Cache Kullanımı
          </h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>Şirket</th>
                  <th>Cache Keys</th>
                  <th>Memory Kullanımı</th>
                  <th>Health Score</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let company of getFilteredCompanies().slice(0, 10)">
                  <td>
                    <strong>{{ company.companyName }}</strong>
                  </td>
                  <td>
                    <span class="badge bg-secondary">{{ company.statistics.totalKeys | number }}</span>
                  </td>
                  <td>{{ formatMemory(company.statistics.totalMemoryUsage) }}</td>
                  <td>
                    <span [class]="getHealthScoreBadgeClass(calculateHealthScore(company))">
                      {{ calculateHealthScore(company) }}%
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Companies Tab -->
    <div *ngIf="activeView === 'companies'" class="tab-content">
      
      <!-- Search and Filter Controls -->
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="input-group">
            <span class="input-group-text">
              <i class="fas fa-search"></i>
            </span>
            <input 
              type="text" 
              class="form-control" 
              placeholder="Şirket adı ile ara..."
              [(ngModel)]="searchTerm">
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex gap-2">
            <select class="form-select" [(ngModel)]="sortBy" (change)="changeSorting(sortBy)">
              <option value="companyName">Şirket Adı</option>
              <option value="totalKeys">Cache Key Sayısı</option>
              <option value="memoryUsage">Memory Kullanımı</option>
            </select>
            <button 
              class="btn btn-outline-secondary"
              (click)="changeSorting(sortBy)"
              title="Sıralama Yönü">
              <i class="fas" [class.fa-sort-up]="sortDirection === 'asc'" [class.fa-sort-down]="sortDirection === 'desc'"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Bulk Selection Controls -->
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="form-check">
          <input 
            class="form-check-input" 
            type="checkbox" 
            id="selectAllCompanies"
            [checked]="selectAll"
            (change)="toggleSelectAll()">
          <label class="form-check-label" for="selectAllCompanies">
            Tümünü Seç
            <span *ngIf="selectedCompanies.size > 0" class="text-muted">
              ({{ selectedCompanies.size }} seçili)
            </span>
          </label>
        </div>
        
        <div *ngIf="selectedCompanies.size > 0">
          <button 
            class="btn btn-danger btn-sm"
            (click)="bulkClearSelectedCompanies()"
            [disabled]="bulkOperationInProgress">
            <i class="fas fa-trash me-2"></i>
            Seçili Cache'leri Temizle ({{ selectedCompanies.size }})
          </button>
        </div>
      </div>

      <!-- Companies Table -->
      <div class="card">
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th width="50">
                    <input 
                      type="checkbox" 
                      [checked]="selectAll"
                      (change)="toggleSelectAll()">
                  </th>
                  <th>Şirket</th>
                  <th>Cache Keys</th>
                  <th>Memory</th>
                  <th>Health</th>
                  <th>Son Güncelleme</th>
                  <th width="120">İşlemler</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let company of getFilteredCompanies()">
                  <td>
                    <input 
                      type="checkbox" 
                      [checked]="selectedCompanies.has(company.companyId)"
                      (change)="toggleCompanySelection(company.companyId)">
                  </td>
                  <td>
                    <div>
                      <strong>{{ company.companyName }}</strong>
                      <br>
                      <small class="text-muted">ID: {{ company.companyId }}</small>
                    </div>
                  </td>
                  <td>
                    <span class="badge bg-primary">{{ company.statistics.totalKeys | number }}</span>
                  </td>
                  <td>{{ formatMemory(company.statistics.totalMemoryUsage) }}</td>
                  <td>
                    <span [class]="getHealthScoreBadgeClass(calculateHealthScore(company))">
                      {{ calculateHealthScore(company) }}%
                    </span>
                  </td>
                  <td>
                    <small class="text-muted">{{ company.statistics.lastUpdated }}</small>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button
                        class="btn btn-outline-info"
                        (click)="viewCompanyDetails(company.companyId, company.companyName)"
                        title="Detayları Görüntüle">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button 
                        class="btn btn-outline-danger"
                        (click)="clearCompanyCache(company.companyId, company.companyName)"
                        title="Cache Temizle">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Operations Tab -->
    <div *ngIf="activeView === 'operations'" class="tab-content">
      <div class="row">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-broom me-2"></i>
                Toplu Cache Temizleme
              </h5>
            </div>
            <div class="card-body">
              <p class="text-muted">
                Seçili şirketlerin cache'lerini toplu olarak temizleyebilirsiniz.
              </p>
              
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Dikkat:</strong> Bu işlem geri alınamaz ve performansı etkileyebilir.
              </div>
              
              <div *ngIf="selectedCompanies.size > 0" class="mb-3">
                <h6>Seçili Şirketler ({{ selectedCompanies.size }}):</h6>
                <div class="d-flex flex-wrap gap-1">
                  <span 
                    *ngFor="let companyId of Array.from(selectedCompanies)" 
                    class="badge bg-secondary">
                    {{ companyId }}
                  </span>
                </div>
              </div>
              
              <button 
                class="btn btn-danger"
                (click)="bulkClearSelectedCompanies()"
                [disabled]="selectedCompanies.size === 0 || bulkOperationInProgress">
                <i class="fas fa-trash me-2"></i>
                <span *ngIf="!bulkOperationInProgress">Seçili Cache'leri Temizle</span>
                <span *ngIf="bulkOperationInProgress">
                  <span class="spinner-border spinner-border-sm me-2"></span>
                  Temizleniyor...
                </span>
              </button>
            </div>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5 class="mb-0">
                <i class="fas fa-chart-line me-2"></i>
                İstatistikler
              </h5>
            </div>
            <div class="card-body">
              <div class="row text-center">
                <div class="col-6 mb-3">
                  <h4 class="text-primary">{{ totalCompanies }}</h4>
                  <small class="text-muted">Toplam Şirket</small>
                </div>
                <div class="col-6 mb-3">
                  <h4 class="text-success">{{ selectedCompanies.size }}</h4>
                  <small class="text-muted">Seçili Şirket</small>
                </div>
                <div class="col-12">
                  <h4 class="text-warning">{{ formatMemory(totalMemoryUsage) }}</h4>
                  <small class="text-muted">Toplam Memory Kullanımı</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<!-- Company Details Modal -->
<div class="modal fade company-details-modal" [class.show]="showCompanyDetailsModal" [style.display]="showCompanyDetailsModal ? 'block' : 'none'"
     tabindex="-1" role="dialog" aria-labelledby="companyDetailsModalLabel" aria-hidden="true" (click)="closeCompanyDetailsModal()">
  <div class="modal-dialog modal-xl" role="document" (click)="$event.stopPropagation()">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="companyDetailsModalLabel">
          <i class="fas fa-building me-2"></i>
          {{ selectedCompanyDetails?.companyName }} - Cache Detayları
        </h5>
      </div>
      <div class="modal-body" *ngIf="selectedCompanyDetails">

        <div class="row">
          <!-- Company Info -->
          <div class="col-md-4">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Şirket Bilgileri</h6>
              </div>
              <div class="card-body">
                <p><strong>Company ID:</strong> {{ selectedCompanyDetails?.details?.company?.companyID || selectedCompanyDetails?.companyId }}</p>
                <p><strong>Şirket Adı:</strong> {{ selectedCompanyDetails?.details?.company?.companyName || selectedCompanyDetails?.companyName }}</p>
                <p><strong>Aktif:</strong>
                  <span class="badge bg-success" *ngIf="selectedCompanyDetails?.details?.company?.isActive">Aktif</span>
                  <span class="badge bg-danger" *ngIf="selectedCompanyDetails?.details?.company?.isActive === false">Pasif</span>
                  <span class="badge bg-secondary" *ngIf="selectedCompanyDetails?.details?.company?.isActive === undefined">Bilinmiyor</span>
                </p>
                <p><strong>Oluşturma Tarihi:</strong> {{ selectedCompanyDetails?.details?.company?.creationDate | date:'dd/MM/yyyy' }}</p>
              </div>
            </div>
          </div>

          <!-- Cache Statistics -->
          <div class="col-md-8">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Cache İstatistikleri</h6>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-3">
                    <div class="text-center">
                      <h4 class="text-primary">{{ selectedCompanyDetails?.details?.cacheDetails?.statistics?.totalKeys || 0 }}</h4>
                      <small class="text-muted">Toplam Key</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h4 class="text-info">{{ selectedCompanyDetails?.details?.cacheDetails?.statistics?.totalMemoryUsageMB || 0 }} MB</h4>
                      <small class="text-muted">Bellek Kullanımı</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h4 class="text-success">{{ selectedCompanyDetails?.details?.cacheDetails?.health?.status || 'Healthy' }}</h4>
                      <small class="text-muted">Durum</small>
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="text-center">
                      <h4 class="text-warning">{{ selectedCompanyDetails?.details?.cacheDetails?.health?.responseTime || 0 }}ms</h4>
                      <small class="text-muted">Yanıt Süresi</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Entity Breakdown & Cache Patterns -->
        <div class="row mt-3">
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-layer-group me-2"></i>Entity Bazlı Dağılım</h6>
              </div>
              <div class="card-body">
                <div *ngFor="let entity of getEntityBreakdown()" class="mb-2">
                  <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-bold">{{ entity.name }}</span>
                    <span class="badge bg-primary">{{ entity.count }} key</span>
                  </div>
                </div>
                <div *ngIf="getEntityBreakdown().length === 0" class="text-muted text-center">
                  <i class="fas fa-info-circle me-2"></i>Entity bilgisi bulunamadı
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-code me-2"></i>Cache Pattern'leri</h6>
              </div>
              <div class="card-body">
                <div *ngFor="let pattern of selectedCompanyDetails?.details?.cacheDetails?.cachePatterns" class="mb-2">
                  <code class="d-block p-2 cache-pattern-code rounded small">{{ pattern }}</code>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


