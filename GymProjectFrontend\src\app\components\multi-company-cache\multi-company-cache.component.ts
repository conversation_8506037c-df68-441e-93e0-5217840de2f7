import { Component, OnInit, OnDestroy } from '@angular/core';
import { CacheAdminService } from '../../services/cache-admin.service';
import { AuthService } from '../../services/auth.service';
import { ToastrService } from 'ngx-toastr';
import { Subscription, interval } from 'rxjs';
import { UserModel } from '../../models/userModel';

@Component({
  selector: 'app-multi-company-cache',
  templateUrl: './multi-company-cache.component.html',
  styleUrls: ['./multi-company-cache.component.css'],
  standalone: false
})
export class MultiCompanyCacheComponent implements OnInit, OnDestroy {
  isLoading = false;
  currentUser: UserModel | null = null;
  
  // Multi-Company Data
  allCompaniesStats: any = null;
  selectedCompanies: Set<number> = new Set();
  selectAll = false;
  
  // Company Selection & Filtering
  searchTerm = '';
  sortBy = 'companyName'; // companyName, totalKeys, memoryUsage, healthScore
  sortDirection = 'asc';
  
  // Real-time Updates
  private realtimeSubscription: Subscription | null = null;
  autoRefresh = true;
  refreshInterval = 10000; // 10 saniye (multi-company için daha uzun)
  
  // Bulk Operations
  bulkOperationInProgress = false;
  
  // UI State
  activeView = 'overview'; // overview, companies, operations

  // Company Details Modal
  showCompanyDetailsModal = false;
  selectedCompanyDetails: any = null;

  // Array helper for template
  Array = Array;

  // Statistics
  totalCompanies = 0;
  totalCacheKeys = 0;
  totalMemoryUsage = 0;
  averageKeysPerCompany = 0;

  constructor(
    private cacheAdminService: CacheAdminService,
    private authService: AuthService,
    private toastr: ToastrService
  ) {}

  async ngOnInit(): Promise<void> {
    this.currentUser = this.authService.currentUserValue;
    
    // Yetki kontrolü
    if (!this.currentUser || !this.hasMultiCompanyAccess()) {
      this.toastr.error('Bu sayfaya erişim yetkiniz yok');
      return;
    }

    await this.loadAllCompaniesStatistics();
    this.startRealtimeUpdates();
  }

  ngOnDestroy(): void {
    this.stopRealtimeUpdates();
  }

  private hasMultiCompanyAccess(): boolean {
    if (!this.currentUser?.role) return false;

    // Role array veya string olabilir
    const roles = Array.isArray(this.currentUser.role) ? this.currentUser.role : [this.currentUser.role];
    return roles.includes('owner') || roles.includes('admin');
  }

  /**
   * Tüm şirketlerin cache istatistiklerini yükler
   */
  async loadAllCompaniesStatistics(): Promise<void> {
    if (this.isLoading) return;
    
    this.isLoading = true;
    try {
      const response = await this.cacheAdminService.getAllCompaniesStatistics().toPromise();
      if (response?.success) {
        this.allCompaniesStats = response.data;
        this.updateStatistics();
        this.toastr.success('Şirket cache istatistikleri güncellendi');
      } else {
        this.toastr.error('Cache istatistikleri alınamadı');
      }
    } catch (error) {
      console.error('Cache istatistikleri yüklenirken hata:', error);
      this.toastr.error('Cache istatistikleri yüklenirken hata oluştu');
    } finally {
      this.isLoading = false;
    }
  }

  private updateStatistics(): void {
    if (!this.allCompaniesStats) return;
    
    this.totalCompanies = this.allCompaniesStats.totalCompanies || 0;
    this.totalCacheKeys = this.allCompaniesStats.totalCacheKeys || 0;
    this.totalMemoryUsage = this.allCompaniesStats.totalMemoryUsage || 0;
    this.averageKeysPerCompany = this.allCompaniesStats.averageKeysPerCompany || 0;
  }

  /**
   * Real-time güncellemeleri başlatır
   */
  private startRealtimeUpdates(): void {
    if (this.autoRefresh) {
      this.realtimeSubscription = interval(this.refreshInterval).subscribe(() => {
        this.loadAllCompaniesStatistics();
      });
    }
  }

  /**
   * Real-time güncellemeleri durdurur
   */
  private stopRealtimeUpdates(): void {
    if (this.realtimeSubscription) {
      this.realtimeSubscription.unsubscribe();
      this.realtimeSubscription = null;
    }
  }

  /**
   * Auto refresh toggle
   */
  toggleAutoRefresh(): void {
    this.autoRefresh = !this.autoRefresh;
    if (this.autoRefresh) {
      this.startRealtimeUpdates();
    } else {
      this.stopRealtimeUpdates();
    }
  }

  /**
   * Şirket seçimi
   */
  toggleCompanySelection(companyId: number): void {
    if (this.selectedCompanies.has(companyId)) {
      this.selectedCompanies.delete(companyId);
    } else {
      this.selectedCompanies.add(companyId);
    }
    this.updateSelectAllState();
  }

  /**
   * Tümünü seç/seçme
   */
  toggleSelectAll(): void {
    this.selectAll = !this.selectAll;
    this.selectedCompanies.clear();
    
    if (this.selectAll && this.allCompaniesStats?.companies) {
      this.allCompaniesStats.companies.forEach((company: any) => {
        this.selectedCompanies.add(company.companyId);
      });
    }
  }

  private updateSelectAllState(): void {
    const totalCompanies = this.allCompaniesStats?.companies?.length || 0;
    this.selectAll = totalCompanies > 0 && this.selectedCompanies.size === totalCompanies;
  }

  /**
   * Filtrelenmiş şirket listesi
   */
  getFilteredCompanies(): any[] {
    if (!this.allCompaniesStats?.companies) return [];
    
    let companies = [...this.allCompaniesStats.companies];
    
    // Arama filtresi
    if (this.searchTerm) {
      companies = companies.filter(company => 
        company.companyName.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
    
    // Sıralama
    companies.sort((a, b) => {
      let aValue, bValue;
      
      switch (this.sortBy) {
        case 'companyName':
          aValue = a.companyName.toLowerCase();
          bValue = b.companyName.toLowerCase();
          break;
        case 'totalKeys':
          aValue = a.statistics.totalKeys;
          bValue = b.statistics.totalKeys;
          break;
        case 'memoryUsage':
          aValue = a.statistics.totalMemoryUsage;
          bValue = b.statistics.totalMemoryUsage;
          break;
        default:
          aValue = a.companyName.toLowerCase();
          bValue = b.companyName.toLowerCase();
      }
      
      if (this.sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
    
    return companies;
  }

  /**
   * Sıralama değiştir
   */
  changeSorting(field: string): void {
    if (this.sortBy === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = field;
      this.sortDirection = 'asc';
    }
  }

  /**
   * Şirket cache detaylarını görüntüle
   */
  async viewCompanyDetails(companyId: number, companyName: string): Promise<void> {
    try {
      this.isLoading = true;
      const response = await this.cacheAdminService.getSpecificCompanyCacheDetails(companyId).toPromise();

      if (response?.success) {
        // Modal veya detay paneli açmak için data'yı hazırla
        this.selectedCompanyDetails = {
          companyId: companyId,
          companyName: companyName,
          details: response.data
        };
        this.showCompanyDetailsModal = true;
        this.toastr.success(`${companyName} detayları yüklendi`);
      } else {
        this.toastr.error('Şirket detayları alınamadı');
      }
    } catch (error) {
      console.error('Şirket detayları yüklenirken hata:', error);
      this.toastr.error('Şirket detayları yüklenirken hata oluştu');
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Şirket detayları modal'ını kapat
   */
  closeCompanyDetailsModal(): void {
    this.showCompanyDetailsModal = false;
    this.selectedCompanyDetails = null;
  }

  /**
   * Entity breakdown'unu hesapla
   */
  getEntityBreakdown(): any[] {
    if (!this.selectedCompanyDetails?.details?.CacheDetails?.Statistics?.KeysByEntity) {
      return [];
    }

    const keysByEntity = this.selectedCompanyDetails.details.CacheDetails.Statistics.KeysByEntity;
    const totalKeys = this.selectedCompanyDetails.details.CacheDetails.Statistics.TotalKeys;

    return Object.entries(keysByEntity).map(([entity, count]: [string, any]) => ({
      name: entity,
      count: count,
      percentage: totalKeys > 0 ? (count / totalKeys) * 100 : 0
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Belirli şirketin cache'ini temizle
   */
  async clearCompanyCache(companyId: number, companyName: string): Promise<void> {
    if (!confirm(`${companyName} şirketinin tüm cache'ini temizlemek istediğinizden emin misiniz?`)) {
      return;
    }

    try {
      const response = await this.cacheAdminService.clearSpecificCompanyCache(companyId).toPromise();
      if (response?.success) {
        this.toastr.success(`${companyName} cache'i başarıyla temizlendi`);
        await this.loadAllCompaniesStatistics(); // Refresh data
      } else {
        this.toastr.error('Cache temizleme işlemi başarısız');
      }
    } catch (error) {
      console.error('Cache temizleme hatası:', error);
      this.toastr.error('Cache temizlenirken hata oluştu');
    }
  }

  /**
   * Seçili şirketlerin cache'lerini toplu temizle
   */
  async bulkClearSelectedCompanies(): Promise<void> {
    if (this.selectedCompanies.size === 0) {
      this.toastr.warning('Lütfen temizlenecek şirketleri seçin');
      return;
    }

    const selectedCount = this.selectedCompanies.size;
    if (!confirm(`Seçili ${selectedCount} şirketin cache'ini temizlemek istediğinizden emin misiniz?`)) {
      return;
    }

    this.bulkOperationInProgress = true;
    try {
      const companyIds = Array.from(this.selectedCompanies);
      const response = await this.cacheAdminService.bulkClearCompaniesCache(companyIds).toPromise();
      
      if (response?.success) {
        this.toastr.success(`${selectedCount} şirketin cache'i başarıyla temizlendi`);
        this.selectedCompanies.clear();
        this.selectAll = false;
        await this.loadAllCompaniesStatistics(); // Refresh data
      } else {
        this.toastr.error('Toplu cache temizleme işlemi başarısız');
      }
    } catch (error) {
      console.error('Toplu cache temizleme hatası:', error);
      this.toastr.error('Toplu cache temizlenirken hata oluştu');
    } finally {
      this.bulkOperationInProgress = false;
    }
  }

  /**
   * Memory formatı
   */
  formatMemory(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Şirket health score hesapla (basit algoritma)
   */
  calculateHealthScore(company: any): number {
    const stats = company.statistics;
    let score = 100;
    
    // Memory usage penalty
    if (stats.totalMemoryUsageMB > 100) score -= 30;
    else if (stats.totalMemoryUsageMB > 50) score -= 15;
    
    // Key count penalty
    if (stats.totalKeys > 10000) score -= 20;
    else if (stats.totalKeys > 5000) score -= 10;
    
    return Math.max(0, score);
  }

  /**
   * Health score badge class
   */
  getHealthScoreBadgeClass(score: number): string {
    if (score >= 80) return 'badge bg-success';
    if (score >= 60) return 'badge bg-warning';
    return 'badge bg-danger';
  }
}
