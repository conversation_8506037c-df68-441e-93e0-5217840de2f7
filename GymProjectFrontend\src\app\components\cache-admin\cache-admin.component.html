<!-- <PERSON>ache Admin Dashboard -->
<div class="container-fluid mt-4 cache-admin-component" [ngClass]="{'fade-in': !isLoading}">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isLoading"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Cache verileri yükleniyor">
  </app-loading-spinner>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="main-content">

    <!-- Page Header with Help Button -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <div class="d-flex align-items-center justify-content-between">
              <div class="d-flex align-items-center gap-2">
                <h5 class="mb-0">
                  <i class="fas fa-database me-2"></i>
                  <PERSON>ache Yönetim Paneli
                </h5>
                <!-- Help Button -->
                <app-help-button
                  guideId="cache-admin"
                  position="inline"
                  size="small"
                  tooltip="Cache yönetimi hakkında yardım al">
                </app-help-button>
              </div>
              
              <!-- Control Buttons -->
              <div class="d-flex gap-2">
                <button 
                  class="btn btn-outline-primary btn-sm"
                  [class.active]="autoRefresh"
                  (click)="toggleAutoRefresh()"
                  [title]="autoRefresh ? 'Otomatik yenilemeyi kapat' : 'Otomatik yenilemeyi aç'">
                  <i class="fas fa-sync-alt" [class.fa-spin]="autoRefresh"></i>
                  {{ autoRefresh ? 'Auto ON' : 'Auto OFF' }}
                </button>
                <button 
                  class="btn btn-primary btn-sm"
                  (click)="refreshData()"
                  title="Verileri yenile">
                  <i class="fas fa-refresh"></i>
                  Yenile
                </button>
              </div>
            </div>
            <p class="text-muted mb-0 mt-1">
              Redis cache durumu, istatistikler ve yönetim araçları
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Cache Health Status -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="alert" [class.alert-success]="cacheHealth?.isConnected" [class.alert-danger]="!cacheHealth?.isConnected">
          <div class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
              <i class="fas fa-heartbeat me-2"></i>
              <strong>Cache Durumu: </strong>
              <span class="ms-2" [ngClass]="getHealthStatusClass()">
                {{ getHealthStatusText() }}
              </span>
              <span *ngIf="cacheHealth?.pingTime" class="ms-3 text-muted">
                Ping: {{ (cacheHealth?.pingTime || 0).toFixed(2) }}ms
              </span>
            </div>
            <div *ngIf="realtimeMetrics?.timestamp" class="text-muted small">
              Son güncelleme: {{ realtimeMetrics.timestamp | date:'HH:mm:ss' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="card-title mb-0">Toplam Key</h5>
                <h2 class="mt-2 mb-0">{{ cacheStatistics?.totalKeys || 0 }}</h2>
                <small>Cache Anahtarları</small>
              </div>
              <i class="fas fa-key fa-3x opacity-50"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-3">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="card-title mb-0">Memory Kullanımı</h5>
                <h2 class="mt-2 mb-0">{{ formatMemory(cacheStatistics?.totalMemoryUsage || 0) }}</h2>
                <small>{{ cacheStatistics?.totalMemoryUsageMB?.toFixed(2) || 0 }} MB</small>
              </div>
              <i class="fas fa-memory fa-3x opacity-50"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-3">
        <div class="card bg-info text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="card-title mb-0">Hit Ratio</h5>
                <h2 class="mt-2 mb-0">{{ formatPercentage(realtimeMetrics?.cacheHitRatio || 0) }}</h2>
                <small>Cache Başarı Oranı</small>
              </div>
              <i class="fas fa-bullseye fa-3x opacity-50"></i>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-3">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="card-title mb-0">Response Time</h5>
                <h2 class="mt-2 mb-0">{{ realtimeMetrics?.performance?.responseTime || 0 }}ms</h2>
                <small>Yanıt Süresi</small>
              </div>
              <i class="fas fa-tachometer-alt fa-3x opacity-50"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Tabs -->
    <div class="row mb-4">
      <div class="col-12">
        <ul class="nav nav-tabs">
          <li class="nav-item">
            <a class="nav-link" 
               [class.active]="activeTab === 'overview'"
               (click)="setActiveTab('overview')"
               href="javascript:void(0)">
              <i class="fas fa-chart-line me-2"></i>Genel Bakış
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" 
               [class.active]="activeTab === 'keys'"
               (click)="setActiveTab('keys')"
               href="javascript:void(0)">
              <i class="fas fa-list me-2"></i>Cache Keys
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" 
               [class.active]="activeTab === 'management'"
               (click)="setActiveTab('management')"
               href="javascript:void(0)">
              <i class="fas fa-tools me-2"></i>Yönetim
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      
      <!-- Overview Tab -->
      <div *ngIf="activeTab === 'overview'" class="tab-pane fade show active">
        <div class="row">
          <!-- Entity Distribution -->
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5><i class="fas fa-chart-pie me-2"></i>Entity Dağılımı</h5>
              </div>
              <div class="card-body">
                <div *ngIf="cacheStatistics?.keysByEntity" class="entity-distribution">
                  <div *ngFor="let entity of cacheStatistics?.keysByEntity | keyvalue" class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                      <span class="fw-bold">{{ entity.key | titlecase }}</span>
                      <span class="badge bg-primary">{{ entity.value }}</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                      <div class="progress-bar" 
                           [style.width.%]="(entity.value / (cacheStatistics?.totalKeys || 1)) * 100">
                      </div>
                    </div>
                  </div>
                </div>
                <div *ngIf="!cacheStatistics?.keysByEntity" class="text-muted text-center py-3">
                  <i class="fas fa-info-circle"></i> Entity dağılımı bulunamadı
                </div>
              </div>
            </div>
          </div>

          <!-- Top Cache Keys -->
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5><i class="fas fa-star me-2"></i>En Çok Kullanılan Keys</h5>
              </div>
              <div class="card-body">
                <div *ngIf="realtimeMetrics?.topCacheKeys?.length > 0" class="top-keys">
                  <div *ngFor="let keyInfo of realtimeMetrics.topCacheKeys" class="mb-2 p-2 border rounded">
                    <div class="d-flex justify-content-between align-items-center">
                      <span class="text-truncate me-2" [title]="keyInfo.key">
                        {{ keyInfo.key }}
                      </span>
                      <div class="d-flex gap-2">
                        <span class="badge bg-info">{{ keyInfo.type }}</span>
                        <span class="badge bg-secondary" *ngIf="keyInfo.ttl > 0">
                          TTL: {{ keyInfo.ttl }}s
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div *ngIf="!realtimeMetrics?.topCacheKeys?.length" class="text-muted text-center py-3">
                  <i class="fas fa-info-circle"></i> Cache key bulunamadı
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Cache Keys Tab -->
      <div *ngIf="activeTab === 'keys'" class="tab-pane fade show active">
        <div class="card">
          <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
              <h5>
                <i class="fas fa-list me-2"></i>Cache Keys Listesi
                <span *ngIf="getSelectedCount() > 0" class="badge bg-primary ms-2">
                  {{ getSelectedCount() }} seçili
                </span>
              </h5>
              <div class="d-flex gap-2">
                <!-- Bulk Operations -->
                <div *ngIf="getSelectedCount() > 0" class="btn-group me-2">
                  <button
                    class="btn btn-danger btn-sm"
                    (click)="bulkDeleteKeys()"
                    [disabled]="bulkOperationInProgress"
                    title="Seçili key'leri sil">
                    <i class="fas fa-trash"></i>
                    <span *ngIf="!bulkOperationInProgress">Seçilileri Sil</span>
                    <span *ngIf="bulkOperationInProgress">
                      <i class="fas fa-spinner fa-spin"></i> Siliniyor...
                    </span>
                  </button>
                  <button
                    class="btn btn-outline-secondary btn-sm"
                    (click)="clearSelection()"
                    title="Seçimi temizle">
                    <i class="fas fa-times"></i>
                  </button>
                </div>

                <input
                  type="text"
                  class="form-control form-control-sm"
                  placeholder="Pattern ara..."
                  [(ngModel)]="filters.keyPattern"
                  (input)="applyFilters()"
                  style="width: 200px;">

                <button
                  class="btn btn-outline-secondary btn-sm"
                  (click)="toggleAdvancedFilters()"
                  [class.active]="showAdvancedFilters"
                  title="Gelişmiş Filtreler">
                  <i class="fas fa-filter"></i>
                  <span *ngIf="hasActiveFilters()" class="badge bg-danger ms-1">!</span>
                </button>

                <div class="btn-group">
                  <button class="btn btn-outline-success btn-sm dropdown-toggle"
                          data-bs-toggle="dropdown" aria-expanded="false"
                          title="Export İşlemleri">
                    <i class="fas fa-download"></i> Export
                  </button>
                  <ul class="dropdown-menu">
                    <li>
                      <a class="dropdown-item" href="#" (click)="exportToCSV(); $event.preventDefault()">
                        <i class="fas fa-file-csv me-2"></i>CSV Olarak Export
                      </a>
                    </li>
                    <li>
                      <a class="dropdown-item" href="#" (click)="exportToExcel(); $event.preventDefault()">
                        <i class="fas fa-file-excel me-2"></i>JSON Olarak Export
                      </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                      <a class="dropdown-item" href="#" (click)="exportStatistics(); $event.preventDefault()">
                        <i class="fas fa-chart-bar me-2"></i>İstatistikleri Export
                      </a>
                    </li>
                  </ul>
                </div>

                <button class="btn btn-primary btn-sm" (click)="loadCacheKeys()">
                  <i class="fas fa-sync-alt"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Advanced Filters Panel -->
          <div *ngIf="showAdvancedFilters" class="card-body border-top advanced-filters-panel">
            <div class="row g-3">
              <div class="col-md-3">
                <label class="form-label">Entity Type</label>
                <select class="form-select form-select-sm" [(ngModel)]="filters.entityType" (change)="applyFilters()">
                  <option value="">Tüm Entity'ler</option>
                  <option *ngFor="let type of getEntityTypes()" [value]="type">{{ type }}</option>
                </select>
              </div>

              <div class="col-md-3">
                <label class="form-label">TTL Aralığı (saniye)</label>
                <div class="d-flex gap-1">
                  <input type="number" class="form-control form-control-sm"
                         placeholder="Min" [(ngModel)]="filters.ttlMin" (input)="applyFilters()">
                  <input type="number" class="form-control form-control-sm"
                         placeholder="Max" [(ngModel)]="filters.ttlMax" (input)="applyFilters()">
                </div>
              </div>

              <div class="col-md-3">
                <label class="form-label">Memory Aralığı (bytes)</label>
                <div class="d-flex gap-1">
                  <input type="number" class="form-control form-control-sm"
                         placeholder="Min" [(ngModel)]="filters.memoryMin" (input)="applyFilters()">
                  <input type="number" class="form-control form-control-sm"
                         placeholder="Max" [(ngModel)]="filters.memoryMax" (input)="applyFilters()">
                </div>
              </div>

              <div class="col-md-3">
                <label class="form-label">Özel Filtreler</label>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox"
                         [(ngModel)]="filters.showExpiring" (change)="applyFilters()" id="showExpiring">
                  <label class="form-check-label" for="showExpiring">
                    Süresi Dolan (< 5dk)
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="checkbox"
                         [(ngModel)]="filters.showPermanent" (change)="applyFilters()" id="showPermanent">
                  <label class="form-check-label" for="showPermanent">
                    Kalıcı Cache'ler
                  </label>
                </div>
              </div>
            </div>

            <div class="row mt-3">
              <div class="col-12">
                <button class="btn btn-outline-danger btn-sm me-2" (click)="resetFilters()">
                  <i class="fas fa-times"></i> Filtreleri Temizle
                </button>
                <small class="text-muted">
                  Toplam {{ getFilteredKeys().length }} / {{ cacheKeys?.keys?.length || 0 }} key gösteriliyor
                </small>
              </div>
            </div>
          </div>

          <div class="card-body">
            <div *ngIf="getFilteredKeys().length" class="table-responsive">
              <table class="table table-hover">
                <thead class="table-dark">
                  <tr>
                    <th style="width: 50px;">
                      <div class="form-check">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          [checked]="selectAll"
                          (change)="toggleSelectAll()"
                          id="selectAllKeys">
                        <label class="form-check-label" for="selectAllKeys"></label>
                      </div>
                    </th>
                    <th>Cache Key</th>
                    <th>Type</th>
                    <th>TTL</th>
                    <th>Memory</th>
                    <th>İşlemler</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let key of getFilteredKeys()">
                    <td>
                      <div class="form-check">
                        <input
                          class="form-check-input"
                          type="checkbox"
                          [checked]="isKeySelected(key.key)"
                          (change)="toggleKeySelection(key.key)"
                          [id]="'key-' + key.key">
                        <label class="form-check-label" [for]="'key-' + key.key"></label>
                      </div>
                    </td>
                    <td>
                      <span class="text-truncate d-inline-block" style="max-width: 300px;" [title]="key.key">
                        {{ key.key }}
                      </span>
                    </td>
                    <td>
                      <span class="badge bg-info">{{ key.type }}</span>
                    </td>
                    <td>
                      <div class="ttl-container">
                        <span class="badge" [ngClass]="getTTLColor(key.ttl || 0)">
                          {{ formatTTL(key.ttl || 0) }}
                        </span>
                        <div *ngIf="key.ttl && key.ttl > 0" class="ttl-progress mt-1">
                          <div class="progress" style="height: 4px;">
                            <div class="progress-bar"
                                 [ngClass]="getTTLProgressColor(key.ttl)"
                                 [style.width.%]="getTTLPercentage(key.ttl, 86400)"
                                 role="progressbar">
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>{{ formatMemory(key.memoryUsage || 0) }}</td>
                    <td>
                      <button 
                        class="btn btn-danger btn-sm"
                        (click)="deleteCacheKey(key.key)"
                        title="Cache key'ini sil">
                        <i class="fas fa-trash"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
              
              <!-- Pagination -->
              <div *ngIf="cacheKeys?.pagination" class="d-flex justify-content-between align-items-center mt-3">
                <div class="text-muted">
                  Toplam {{ cacheKeys?.pagination?.totalCount }} kayıt,
                  Sayfa {{ cacheKeys?.pagination?.currentPage }} / {{ cacheKeys?.pagination?.totalPages }}
                </div>
                <pagination-controls
                  (pageChange)="onPageChange($event)"
                  [maxSize]="5"
                  [directionLinks]="true"
                  [autoHide]="true">
                </pagination-controls>
              </div>
            </div>
            
            <div *ngIf="!getFilteredKeys().length" class="text-center py-4">
              <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
              <p class="text-muted" *ngIf="!hasActiveFilters()">Henüz cache key bulunamadı</p>
              <p class="text-muted" *ngIf="hasActiveFilters()">Filtrelere uygun cache key bulunamadı</p>
              <button *ngIf="hasActiveFilters()" class="btn btn-outline-primary btn-sm" (click)="resetFilters()">
                <i class="fas fa-times"></i> Filtreleri Temizle
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Management Tab -->
      <div *ngIf="activeTab === 'management'" class="tab-pane fade show active">
        <div class="row">
          <!-- Cache Operations -->
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5><i class="fas fa-tools me-2"></i>Cache İşlemleri</h5>
              </div>
              <div class="card-body">
                <div class="d-grid gap-3">
                  <button 
                    class="btn btn-warning"
                    [disabled]="isWarming"
                    (click)="warmupCache()">
                    <i class="fas fa-fire" [class.fa-spin]="isWarming"></i>
                    {{ isWarming ? 'Cache Warmup Yapılıyor...' : 'Cache Warmup' }}
                  </button>
                  
                  <button 
                    class="btn btn-danger"
                    [disabled]="isClearing"
                    (click)="clearTenantCache()">
                    <i class="fas fa-trash" [class.fa-spin]="isClearing"></i>
                    {{ isClearing ? 'Cache Temizleniyor...' : 'Tüm Cache\'i Temizle' }}
                  </button>
                </div>
                
                <hr>
                
                <h6>Pattern Bazlı Temizleme</h6>
                <div class="input-group mb-2">
                  <input 
                    type="text" 
                    class="form-control" 
                    placeholder="Örn: member:*"
                    #patternInput>
                  <button 
                    class="btn btn-outline-danger"
                    (click)="clearCacheByPattern(patternInput.value)">
                    <i class="fas fa-broom"></i>
                    Temizle
                  </button>
                </div>
                <small class="text-muted">
                  Belirli pattern'e uygun cache'leri temizler
                </small>
              </div>
            </div>
          </div>

          <!-- Cache Information -->
          <div class="col-md-6">
            <div class="card">
              <div class="card-header">
                <h5><i class="fas fa-info-circle me-2"></i>Cache Bilgileri</h5>
              </div>
              <div class="card-body">
                <div *ngIf="cacheHealth" class="cache-info">
                  <div class="row mb-2">
                    <div class="col-6"><strong>Bağlantı Durumu:</strong></div>
                    <div class="col-6" [ngClass]="getHealthStatusClass()">
                      {{ getHealthStatusText() }}
                    </div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-6"><strong>Ping Süresi:</strong></div>
                    <div class="col-6">{{ (cacheHealth.pingTime || 0).toFixed(2) }}ms</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-6"><strong>Response Time:</strong></div>
                    <div class="col-6">{{ cacheHealth.responseTime || 0 }}ms</div>
                  </div>
                  <div class="row mb-2" *ngIf="realtimeMetrics?.performance">
                    <div class="col-6"><strong>Bağlantı Sayısı:</strong></div>
                    <div class="col-6">{{ realtimeMetrics.performance.connectionCount || 0 }}</div>
                  </div>
                </div>
                
                <hr>
                
                <div *ngIf="cacheStatistics" class="cache-stats">
                  <h6>İstatistikler</h6>
                  <div class="row mb-2">
                    <div class="col-6"><strong>Company ID:</strong></div>
                    <div class="col-6">{{ cacheStatistics.companyId }}</div>
                  </div>
                  <div class="row mb-2">
                    <div class="col-6"><strong>Son Güncelleme:</strong></div>
                    <div class="col-6">{{ cacheStatistics.lastUpdated | date:'dd/MM/yyyy HH:mm' }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
