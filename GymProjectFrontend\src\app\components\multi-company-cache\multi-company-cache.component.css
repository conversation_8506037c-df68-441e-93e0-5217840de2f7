/* Multi Company Cache Component - Global Theme Compatible */
.multi-company-cache-component {
  padding: 20px;
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
}

/* Header Styling */
.multi-company-cache-component h2 {
  color: var(--primary-color);
  font-weight: 600;
}

/* Navigation Tabs */
.multi-company-cache-component .nav-tabs {
  border-bottom: 1px solid var(--border-color);
}

.multi-company-cache-component .nav-tabs .nav-link {
  color: var(--text-color);
  border: 1px solid transparent;
  background-color: transparent;
}

.multi-company-cache-component .nav-tabs .nav-link:hover {
  border-color: var(--border-color);
  background-color: var(--sidebar-hover);
}

.multi-company-cache-component .nav-tabs .nav-link.active {
  color: var(--primary-color);
  background-color: var(--card-bg-color);
  border-color: var(--border-color) var(--border-color) var(--card-bg-color);
}

/* Card Enhancements */
.multi-company-cache-component .card {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.multi-company-cache-component .card-header {
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
}

.multi-company-cache-component .card-body {
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* Form Controls */
.multi-company-cache-component .form-control,
.multi-company-cache-component .form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
}

.multi-company-cache-component .form-control:focus,
.multi-company-cache-component .form-select:focus {
  background-color: var(--input-bg);
  border-color: var(--primary-color);
  color: var(--input-text);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* Company Details Modal - Dark Mode Support */
.company-details-modal .modal-content {
  background-color: var(--card-bg-color) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.company-details-modal .modal-header {
  background-color: var(--card-bg-color) !important;
  border-bottom-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.company-details-modal .modal-title {
  color: var(--text-color) !important;
}

.company-details-modal .btn-close {
  filter: var(--btn-close-filter, none);
}

.company-details-modal .modal-body {
  background-color: var(--card-bg-color) !important;
  color: var(--text-color) !important;
}

.company-details-modal .modal-footer {
  background-color: var(--card-bg-color) !important;
  border-top-color: var(--border-color) !important;
}

.company-details-modal .alert-info {
  background-color: var(--info-bg, #d1ecf1) !important;
  border-color: var(--info-border, #bee5eb) !important;
  color: var(--info-text, #0c5460) !important;
}

/* Dark mode specific styles */
[data-theme="dark"] .company-details-modal .btn-close {
  filter: invert(1) grayscale(100%) brightness(200%);
}

[data-theme="dark"] .company-details-modal .alert-info {
  background-color: rgba(13, 202, 240, 0.1) !important;
  border-color: rgba(13, 202, 240, 0.2) !important;
  color: #9eeaf9 !important;
}

/* Cache Pattern Code Blocks - Dark Mode Fix */
.company-details-modal .bg-light {
  background-color: var(--input-bg) !important;
  color: var(--input-text) !important;
  border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .company-details-modal .bg-light {
  background-color: var(--input-bg) !important;
  color: var(--input-text) !important;
  border: 1px solid var(--input-border) !important;
}

[data-theme="dark"] .company-details-modal code {
  background-color: var(--input-bg) !important;
  color: var(--input-text) !important;
  border: 1px solid var(--input-border) !important;
}

/* Cache Pattern Code Specific Styling */
.cache-pattern-code {
  background-color: var(--input-bg) !important;
  color: var(--input-text) !important;
  border: 1px solid var(--input-border) !important;
  font-family: 'Courier New', Consolas, monospace !important;
  font-size: 0.85rem !important;
}

/* Buttons */
.multi-company-cache-component .btn-primary {
  background-color: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
}

.multi-company-cache-component .btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.multi-company-cache-component .btn-outline-secondary {
  border-color: var(--border-color);
  color: var(--text-color);
}

.multi-company-cache-component .btn-outline-secondary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.multi-company-cache-component .btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.multi-company-cache-component .btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.multi-company-cache-component .btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

/* Table Styling */
.multi-company-cache-component .table {
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

.multi-company-cache-component .table thead th {
  background-color: var(--card-bg-color);
  border-bottom: 2px solid var(--primary-color);
  color: var(--text-color);
  font-weight: 600;
}

.multi-company-cache-component .table tbody tr:hover {
  background-color: var(--sidebar-hover);
}

.multi-company-cache-component .table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--sidebar-hover);
}

/* Badge Styling */
.multi-company-cache-component .badge {
  color: white;
}

.multi-company-cache-component .badge.bg-primary {
  background-color: var(--primary-color) !important;
}

.multi-company-cache-component .badge.bg-success {
  background-color: #28a745 !important;
}

.multi-company-cache-component .badge.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.multi-company-cache-component .badge.bg-danger {
  background-color: #dc3545 !important;
}

.multi-company-cache-component .badge.bg-info {
  background-color: #17a2b8 !important;
}

.multi-company-cache-component .badge.bg-secondary {
  background-color: var(--text-muted) !important;
}

/* Loading States */
.multi-company-cache-component .spinner-border {
  color: var(--spinner-color);
}

/* Text Colors */
.multi-company-cache-component .text-muted {
  color: var(--text-muted) !important;
}

.multi-company-cache-component .text-primary {
  color: var(--primary-color) !important;
}

.multi-company-cache-component .text-success {
  color: #28a745 !important;
}

.multi-company-cache-component .text-danger {
  color: #dc3545 !important;
}

.multi-company-cache-component .text-warning {
  color: #ffc107 !important;
}

/* Company Cards */
.multi-company-cache-component .company-card {
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s ease;
}

.multi-company-cache-component .company-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.multi-company-cache-component .company-card.active {
  border-left-color: #28a745;
  background-color: rgba(40, 167, 69, 0.05);
}

.multi-company-cache-component .company-card.warning {
  border-left-color: #ffc107;
  background-color: rgba(255, 193, 7, 0.05);
}

.multi-company-cache-component .company-card.danger {
  border-left-color: #dc3545;
  background-color: rgba(220, 53, 69, 0.05);
}

/* Statistics Cards */
.multi-company-cache-component .stat-card {
  text-align: center;
  padding: 1.5rem;
  border-radius: 0.5rem;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
}

.multi-company-cache-component .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.multi-company-cache-component .stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Progress Bars */
.multi-company-cache-component .progress {
  background-color: var(--sidebar-hover);
}

.multi-company-cache-component .progress-bar {
  background-color: var(--primary-color);
}

/* Alert Styling */
.multi-company-cache-component .alert {
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

.multi-company-cache-component .alert-info {
  background-color: rgba(23, 162, 184, 0.1);
  border-color: rgba(23, 162, 184, 0.3);
  color: #17a2b8;
}

.multi-company-cache-component .alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.3);
  color: #ffc107;
}

.multi-company-cache-component .alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.3);
  color: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
  .multi-company-cache-component {
    padding: 10px;
  }
  
  .multi-company-cache-component .stat-value {
    font-size: 1.5rem;
  }
  
  .multi-company-cache-component .company-card {
    margin-bottom: 1rem;
  }
}
