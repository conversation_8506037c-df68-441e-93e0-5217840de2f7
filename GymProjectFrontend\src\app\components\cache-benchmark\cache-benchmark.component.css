/* Cache Benchmark Component - Global Theme Compatible */
.cache-benchmark-component {
  padding: 20px;
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
}

/* Header Styling */
.cache-benchmark-component h2 {
  color: var(--primary-color);
  font-weight: 600;
}

/* Card Enhancements */
.cache-benchmark-component .card {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.cache-benchmark-component .card-header {
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
}

.cache-benchmark-component .card-body {
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* Form Controls */
.cache-benchmark-component .form-control,
.cache-benchmark-component .form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
}

.cache-benchmark-component .form-control:focus,
.cache-benchmark-component .form-select:focus {
  background-color: var(--input-bg);
  border-color: var(--primary-color);
  color: var(--input-text);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* Buttons */
.cache-benchmark-component .btn-primary {
  background-color: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
}

.cache-benchmark-component .btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.cache-benchmark-component .btn-outline-secondary {
  border-color: var(--border-color);
  color: var(--text-color);
}

.cache-benchmark-component .btn-outline-secondary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.cache-benchmark-component .btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.cache-benchmark-component .btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

/* Table Styling */
.cache-benchmark-component .table {
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

.cache-benchmark-component .table thead th {
  background-color: var(--card-bg-color);
  border-bottom: 2px solid var(--primary-color);
  color: var(--text-color);
  font-weight: 600;
}

.cache-benchmark-component .table tbody tr:hover {
  background-color: var(--sidebar-hover);
}

/* Badge Styling */
.cache-benchmark-component .badge {
  color: white;
}

.cache-benchmark-component .badge.bg-primary {
  background-color: var(--primary-color) !important;
}

.cache-benchmark-component .badge.bg-success {
  background-color: #28a745 !important;
}

.cache-benchmark-component .badge.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.cache-benchmark-component .badge.bg-danger {
  background-color: #dc3545 !important;
}

.cache-benchmark-component .badge.bg-info {
  background-color: #17a2b8 !important;
}

.cache-benchmark-component .badge.bg-secondary {
  background-color: var(--text-muted) !important;
}

/* Loading States */
.cache-benchmark-component .spinner-border {
  color: var(--spinner-color);
}

/* Text Colors */
.cache-benchmark-component .text-muted {
  color: var(--text-muted) !important;
}

.cache-benchmark-component .text-primary {
  color: var(--primary-color) !important;
}

.cache-benchmark-component .text-success {
  color: #28a745 !important;
}

.cache-benchmark-component .text-danger {
  color: #dc3545 !important;
}

.cache-benchmark-component .text-warning {
  color: #ffc107 !important;
}

/* Performance Indicators */
.cache-benchmark-component .performance-indicator {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.cache-benchmark-component .performance-excellent {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.cache-benchmark-component .performance-good {
  background-color: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
  border: 1px solid rgba(23, 162, 184, 0.3);
}

.cache-benchmark-component .performance-average {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.cache-benchmark-component .performance-poor {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

/* Progress Bars */
.cache-benchmark-component .progress {
  background-color: var(--sidebar-hover);
}

.cache-benchmark-component .progress-bar {
  background-color: var(--primary-color);
}

/* Benchmark Results */
.cache-benchmark-component .benchmark-result {
  padding: 1rem;
  margin: 0.5rem 0;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
}

.cache-benchmark-component .benchmark-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.cache-benchmark-component .metric-name {
  font-weight: 500;
  color: var(--text-color);
}

.cache-benchmark-component .metric-value {
  font-weight: 600;
  color: var(--primary-color);
}

/* Chart Container */
.cache-benchmark-component .chart-container {
  position: relative;
  width: 100%;
  height: 300px;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cache-benchmark-component {
    padding: 10px;
  }
  
  .cache-benchmark-component .chart-container {
    height: 250px;
  }
  
  .cache-benchmark-component .benchmark-metric {
    flex-direction: column;
    align-items: flex-start;
  }
}
