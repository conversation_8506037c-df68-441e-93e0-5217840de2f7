/* <PERSON><PERSON> Admin Component Styles */

/* Advanced Filters Panel - Dark Mode Support */
.advanced-filters-panel {
  background-color: var(--sidebar-hover) !important;
  border-color: var(--border-color) !important;
  color: var(--text-color) !important;
}

.advanced-filters-panel .form-label {
  color: var(--text-color) !important;
  font-weight: 500;
}

.advanced-filters-panel .form-control,
.advanced-filters-panel .form-select {
  background-color: var(--input-bg) !important;
  border-color: var(--input-border) !important;
  color: var(--input-text) !important;
}

.advanced-filters-panel .form-control:focus,
.advanced-filters-panel .form-select:focus {
  background-color: var(--input-bg) !important;
  border-color: var(--primary-color) !important;
  color: var(--input-text) !important;
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25) !important;
}

.advanced-filters-panel .form-check-input {
  background-color: var(--input-bg) !important;
  border-color: var(--input-border) !important;
}

.advanced-filters-panel .form-check-input:checked {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.advanced-filters-panel .form-check-label {
  color: var(--text-color) !important;
}

.advanced-filters-panel .text-muted {
  color: var(--text-muted) !important;
}

.advanced-filters-panel .btn-outline-danger {
  border-color: #dc3545 !important;
  color: #dc3545 !important;
  background-color: transparent !important;
}

.advanced-filters-panel .btn-outline-danger:hover {
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
  color: white !important;
}

/* Legacy bg-light support for other components */
.bg-light {
  background-color: var(--card-bg-color) !important;
  color: var(--text-color) !important;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Statistics Cards */
.card {
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 2px 10px var(--shadow-color);
  transition: all 0.3s ease;
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px var(--shadow-color);
}

.card-header {
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
  border-radius: 12px 12px 0 0 !important;
  color: var(--text-color);
}

.card-body {
  padding: 1.5rem;
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* Statistics Cards Colors */
.bg-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
}

.bg-info {
  background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%) !important;
}

.bg-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.bg-danger {
  background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%) !important;
}

/* Icons */
.opacity-50 {
  opacity: 0.5;
}

/* Navigation Tabs */
.nav-tabs {
  border-bottom: 2px solid var(--border-color);
}

.nav-tabs .nav-link {
  border: none;
  border-radius: 8px 8px 0 0;
  color: var(--text-muted);
  font-weight: 500;
  padding: 12px 20px;
  margin-right: 5px;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  background-color: var(--sidebar-hover);
  color: var(--text-color);
  border-color: transparent;
}

.nav-tabs .nav-link.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Tab Content */
.tab-content {
  padding-top: 20px;
}

/* Entity Distribution */
.entity-distribution .progress {
  background-color: var(--sidebar-hover);
  border-radius: 4px;
}

.entity-distribution .progress-bar {
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 4px;
}

/* Top Cache Keys */
.top-keys .border {
  border-color: var(--border-color) !important;
  transition: all 0.3s ease;
  background-color: var(--card-bg-color);
}

.top-keys .border:hover {
  border-color: var(--primary-color) !important;
  background-color: var(--sidebar-hover);
}

/* Cache Keys Table */
.table {
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

.table-dark {
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
}

.table-hover tbody tr:hover {
  background-color: var(--sidebar-hover);
}

/* Badges */
.badge {
  font-size: 0.75em;
  padding: 0.5em 0.75em;
  border-radius: 6px;
  font-weight: 500;
}

.badge.bg-info {
  background-color: var(--accent-color) !important;
  color: white !important;
}

.badge.bg-success {
  background-color: #28a745 !important;
  color: white !important;
}

.badge.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.badge.bg-secondary {
  background-color: var(--text-muted) !important;
  color: white !important;
}

.badge.bg-primary {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* Buttons */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: #212529;
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
  color: white;
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  color: white;
}

.btn-outline-danger {
  border: 2px solid #dc3545;
  color: #dc3545;
  background: transparent;
}

.btn-outline-danger:hover {
  background: #dc3545;
  color: white;
}

/* Auto Refresh Button */
.btn.active {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  color: white;
  border-color: #28a745;
}

/* Alerts */
.alert {
  border: none;
  border-radius: 10px;
  padding: 1rem 1.5rem;
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

.alert-success {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(30, 126, 52, 0.1) 100%);
  border-left: 4px solid #28a745;
  color: #28a745;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(189, 33, 48, 0.1) 100%);
  border-left: 4px solid #dc3545;
  color: #dc3545;
}

/* Form Controls */
.form-control {
  border-radius: 8px;
  border: 2px solid var(--input-border);
  background-color: var(--input-bg);
  color: var(--input-text);
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
  background-color: var(--input-bg);
  color: var(--input-text);
}

/* Input Groups */
.input-group .form-control {
  border-radius: 8px 0 0 8px;
}

.input-group .btn {
  border-radius: 0 8px 8px 0;
}

/* Cache Info Section */
.cache-info .row {
  margin-bottom: 0.5rem;
}

.cache-info .row:last-child {
  margin-bottom: 0;
}

/* Text Colors */
.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }
  
  .nav-tabs .nav-link {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
  
  .btn {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
  }
  
  .table-responsive {
    font-size: 0.9rem;
  }
}

/* Loading States */
.fa-spin {
  animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Disabled States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Pagination */
.pagination {
  justify-content: center;
}

.pagination .page-link {
  border-radius: 6px;
  margin: 0 2px;
  border: 1px solid var(--border-color);
  color: var(--primary-color);
  background-color: var(--card-bg-color);
}

.pagination .page-link:hover {
  background-color: var(--sidebar-hover);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.pagination .page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* Truncate Text */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* TTL Container Styles */
.ttl-container {
  min-width: 120px;
}

.ttl-progress {
  width: 100%;
}

.ttl-progress .progress {
  background-color: var(--sidebar-hover);
  border-radius: 2px;
  overflow: hidden;
}

.ttl-progress .progress-bar {
  transition: width 0.3s ease;
  border-radius: 2px;
}

/* TTL Badge Colors */
.badge.text-success {
  background-color: rgba(40, 167, 69, 0.1) !important;
  color: #28a745 !important;
  border: 1px solid #28a745;
}

.badge.text-warning {
  background-color: rgba(255, 193, 7, 0.1) !important;
  color: #ffc107 !important;
  border: 1px solid #ffc107;
}

.badge.text-danger {
  background-color: rgba(220, 53, 69, 0.1) !important;
  color: #dc3545 !important;
  border: 1px solid #dc3545;
}

.badge.text-info {
  background-color: rgba(23, 162, 184, 0.1) !important;
  color: #17a2b8 !important;
  border: 1px solid #17a2b8;
}

/* Dark Mode Specific Overrides */
[data-theme="dark"] .cache-admin-component {
  background-color: var(--background-color);
  color: var(--text-color);
}

[data-theme="dark"] .cache-admin-component .table {
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

[data-theme="dark"] .cache-admin-component .table th {
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  border-color: var(--border-color);
}

[data-theme="dark"] .cache-admin-component .table td {
  border-color: var(--border-color);
}

[data-theme="dark"] .cache-admin-component .form-control::placeholder {
  color: var(--text-muted);
  opacity: 0.8;
}

[data-theme="dark"] .cache-admin-component .btn:hover {
  box-shadow: 0 4px 12px var(--shadow-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }

  .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }

  .table {
    font-size: 0.875rem;
  }

  .nav-tabs .nav-link {
    padding: 8px 12px;
    font-size: 0.875rem;
  }
}
