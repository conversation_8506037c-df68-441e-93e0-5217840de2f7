/* <PERSON><PERSON> Alerts Component - Global Theme Compatible */
.cache-alerts-component {
  padding: 20px;
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
}

/* Header Styling */
.cache-alerts-component h2 {
  color: var(--primary-color);
  font-weight: 600;
}

/* Card Enhancements */
.cache-alerts-component .card {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.cache-alerts-component .card-header {
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
}

.cache-alerts-component .card-body {
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* Form Controls */
.cache-alerts-component .form-control,
.cache-alerts-component .form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
}

.cache-alerts-component .form-control:focus,
.cache-alerts-component .form-select:focus {
  background-color: var(--input-bg);
  border-color: var(--primary-color);
  color: var(--input-text);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* Buttons */
.cache-alerts-component .btn-primary {
  background-color: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
}

.cache-alerts-component .btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.cache-alerts-component .btn-outline-secondary {
  border-color: var(--border-color);
  color: var(--text-color);
}

.cache-alerts-component .btn-outline-secondary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.cache-alerts-component .btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.cache-alerts-component .btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.cache-alerts-component .btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

/* Alert Items */
.cache-alerts-component .alert-item {
  padding: 1rem;
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  background-color: var(--card-bg-color);
  transition: all 0.3s ease;
  cursor: pointer;
}

.cache-alerts-component .alert-item:hover {
  transform: translateX(5px);
  box-shadow: 0 2px 8px var(--shadow-color);
}

.cache-alerts-component .alert-item.unread {
  border-left: 4px solid var(--primary-color);
  background-color: rgba(67, 97, 238, 0.05);
}

.cache-alerts-component .alert-item.read {
  opacity: 0.7;
}

/* Alert Types */
.cache-alerts-component .alert-error {
  border-left-color: #dc3545 !important;
  background-color: rgba(220, 53, 69, 0.05) !important;
}

.cache-alerts-component .alert-warning {
  border-left-color: #ffc107 !important;
  background-color: rgba(255, 193, 7, 0.05) !important;
}

.cache-alerts-component .alert-success {
  border-left-color: #28a745 !important;
  background-color: rgba(40, 167, 69, 0.05) !important;
}

.cache-alerts-component .alert-info {
  border-left-color: #17a2b8 !important;
  background-color: rgba(23, 162, 184, 0.05) !important;
}

/* Severity Indicators */
.cache-alerts-component .severity-critical {
  border-width: 4px !important;
  box-shadow: 0 0 10px rgba(220, 53, 69, 0.3);
}

.cache-alerts-component .severity-high {
  border-width: 3px !important;
}

.cache-alerts-component .severity-medium {
  border-width: 2px !important;
}

.cache-alerts-component .severity-low {
  border-width: 1px !important;
}

/* Badge Styling */
.cache-alerts-component .badge {
  color: white;
}

.cache-alerts-component .badge.bg-primary {
  background-color: var(--primary-color) !important;
}

.cache-alerts-component .badge.bg-success {
  background-color: #28a745 !important;
}

.cache-alerts-component .badge.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.cache-alerts-component .badge.bg-danger {
  background-color: #dc3545 !important;
}

.cache-alerts-component .badge.bg-info {
  background-color: #17a2b8 !important;
}

.cache-alerts-component .badge.bg-secondary {
  background-color: var(--text-muted) !important;
}

/* Loading States */
.cache-alerts-component .spinner-border {
  color: var(--spinner-color);
}

/* Text Colors */
.cache-alerts-component .text-muted {
  color: var(--text-muted) !important;
}

.cache-alerts-component .text-primary {
  color: var(--primary-color) !important;
}

.cache-alerts-component .text-success {
  color: #28a745 !important;
}

.cache-alerts-component .text-danger {
  color: #dc3545 !important;
}

.cache-alerts-component .text-warning {
  color: #ffc107 !important;
}

/* Alert Statistics */
.cache-alerts-component .alert-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.cache-alerts-component .stat-card {
  text-align: center;
  padding: 1.5rem;
  border-radius: 0.5rem;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
}

.cache-alerts-component .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.cache-alerts-component .stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Filter Controls */
.cache-alerts-component .filter-controls {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

/* Settings Panel */
.cache-alerts-component .settings-panel {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-top: 1rem;
}

.cache-alerts-component .threshold-input {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  color: var(--input-text);
  border-radius: 0.25rem;
  padding: 0.5rem;
  width: 100%;
}

.cache-alerts-component .threshold-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* Pagination */
.cache-alerts-component .pagination {
  justify-content: center;
  margin-top: 2rem;
}

.cache-alerts-component .page-link {
  background-color: var(--card-bg-color);
  border-color: var(--border-color);
  color: var(--text-color);
}

.cache-alerts-component .page-link:hover {
  background-color: var(--sidebar-hover);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.cache-alerts-component .page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* Animation Effects */
.cache-alerts-component .alert-item {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .cache-alerts-component {
    padding: 10px;
  }
  
  .cache-alerts-component .alert-stats {
    grid-template-columns: 1fr;
  }
  
  .cache-alerts-component .stat-value {
    font-size: 1.5rem;
  }
  
  .cache-alerts-component .filter-controls {
    padding: 0.75rem;
  }
}
