/* Cache Trends Component - Global Theme Compatible */
.cache-trends-component {
  padding: 20px;
  background-color: var(--background-color);
  color: var(--text-color);
  min-height: 100vh;
}

/* Header Styling */
.cache-trends-component h2 {
  color: var(--primary-color);
  font-weight: 600;
}

/* Chart Container */
.chart-container {
  position: relative;
  width: 100%;
  height: 400px;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, 
    rgba(67, 97, 238, 0.1) 0%, 
    rgba(63, 55, 201, 0.1) 100%);
  border: 2px dashed var(--border-color);
  border-radius: 8px;
}

.chart-placeholder::before {
  content: "📊 Chart Area";
  font-size: 1.2em;
  color: var(--text-muted);
}

/* Card Enhancements */
.cache-trends-component .card {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.cache-trends-component .card-header {
  background-color: var(--card-bg-color);
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
}

.cache-trends-component .card-body {
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

/* Form Controls */
.cache-trends-component .form-control,
.cache-trends-component .form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
}

.cache-trends-component .form-control:focus,
.cache-trends-component .form-select:focus {
  background-color: var(--input-bg);
  border-color: var(--primary-color);
  color: var(--input-text);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* Buttons */
.cache-trends-component .btn-primary {
  background-color: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  color: var(--btn-primary-text);
}

.cache-trends-component .btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.cache-trends-component .btn-outline-secondary {
  border-color: var(--border-color);
  color: var(--text-color);
}

.cache-trends-component .btn-outline-secondary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

/* Table Styling */
.cache-trends-component .table {
  background-color: var(--card-bg-color);
  color: var(--text-color);
}

.cache-trends-component .table thead th {
  background-color: var(--card-bg-color);
  border-bottom: 2px solid var(--primary-color);
  color: var(--text-color);
  font-weight: 600;
}

.cache-trends-component .table tbody tr:hover {
  background-color: var(--sidebar-hover);
}

/* Badge Styling */
.cache-trends-component .badge {
  color: white;
}

.cache-trends-component .badge.bg-primary {
  background-color: var(--primary-color) !important;
}

.cache-trends-component .badge.bg-success {
  background-color: #28a745 !important;
}

.cache-trends-component .badge.bg-warning {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

.cache-trends-component .badge.bg-danger {
  background-color: #dc3545 !important;
}

.cache-trends-component .badge.bg-info {
  background-color: #17a2b8 !important;
}

.cache-trends-component .badge.bg-secondary {
  background-color: var(--text-muted) !important;
}

/* Loading States */
.cache-trends-component .spinner-border {
  color: var(--spinner-color);
}

/* Text Colors */
.cache-trends-component .text-muted {
  color: var(--text-muted) !important;
}

.cache-trends-component .text-primary {
  color: var(--primary-color) !important;
}

/* Chart Legend */
.cache-trends-component .chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.cache-trends-component .legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cache-trends-component .legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Metric Cards */
.cache-trends-component .metric-value {
  font-size: 1.2em;
  font-weight: 600;
  color: var(--primary-color);
}

.cache-trends-component .metric-label {
  font-size: 0.85em;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Status Indicators */
.cache-trends-component .status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.5rem;
}

.cache-trends-component .status-good {
  background-color: #28a745;
}

.cache-trends-component .status-warning {
  background-color: #ffc107;
}

.cache-trends-component .status-critical {
  background-color: #dc3545;
}

/* Trend Analysis */
.cache-trends-component .trend-card {
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s ease;
}

.cache-trends-component .trend-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.cache-trends-component .trend-up {
  border-left-color: #28a745;
}

.cache-trends-component .trend-down {
  border-left-color: #dc3545;
}

.cache-trends-component .trend-stable {
  border-left-color: #17a2b8;
}

/* Time Range Selector */
.cache-trends-component .time-range-selector {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .cache-trends-component {
    padding: 10px;
  }
  
  .cache-trends-component .chart-container {
    height: 300px;
  }
  
  .cache-trends-component .chart-legend {
    flex-direction: column;
  }
}
